/// <reference types="three" />

declare module '*.glb';
declare module '*.png';

declare module 'meshline' {
  import * as THREE from 'three';

  export class MeshLineGeometry extends THREE.BufferGeometry {
    setPoints(points: THREE.Vector3[]): void;
  }

  export class MeshLineMaterial extends THREE.Material {
    color: THREE.Color;
    depthTest: boolean;
    resolution: THREE.Vector2;
    useMap: boolean;
    map: THREE.Texture | null;
    repeat: [number, number];
    lineWidth: number;
  }
}

declare global {
  namespace JSX {
    interface IntrinsicElements {
      meshLineGeometry: React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
      meshLineMaterial: React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement> & {
        color?: string | number;
        depthTest?: boolean;
        resolution?: [number, number];
        useMap?: boolean;
        map?: THREE.Texture;
        repeat?: [number, number];
        lineWidth?: number;
      }, HTMLElement>;
    }
  }
}